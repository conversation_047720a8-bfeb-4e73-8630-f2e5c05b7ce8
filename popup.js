function updatePopupWithAccountValue(message) {
  const accountValueElement = document.getElementById('accountValue');
  const codeVersionElement = document.getElementById('codeVersion');
  const isSpaElement = document.getElementById('isSpa');
  const impersonateBtn = document.getElementById('impersonateLink');
  const isCodeV3 = message.data.isEmbedExists


  // Construct the backoffice URL based on environment
  let backofficeUrl = 'https://backoffice.optimonk.com';
  if (message.data.environment && message.data.environment !== 'live') {
    backofficeUrl = 'https://' + message.data.environment + '-backoffice.optimonk.com';
  }
  if (message.data.accountId && isCodeV3) {
    accountValueElement.textContent = 'OptiMonk ID: ' + message.data.accountId + ' (' + message.data.environment + ')';
    codeVersionElement.textContent = 'Code version: Latest (v3)';
    impersonateBtn.href = backofficeUrl + '/api/impersonate/' + message.data.accountId;
  } else if (message.data.accountId && !isCodeV3) {
    accountValueElement.textContent = 'OptiMonk ID: ' + message.data.accountId;
    codeVersionElement.textContent = 'Code version: Deprecated (v2)';
    codeVersionElement.classList.add('error');
    impersonateBtn.href = backofficeUrl + '/api/impersonate/' + message.data.accountId;
  } else {
    accountValueElement.textContent = 'OptiMonk code not found.';
    accountValueElement.classList.add('error');
    impersonateBtn.setAttribute('aria-disabled', true);
    impersonateBtn.classList.add('disabled');
  }
}

function updatePopupWithPlatform (message) {
  const detectedPlatformElement = document.getElementById('detectedPlatform');
  detectedPlatformElement.textContent = "Platform: " + message.platform;
}

function updatePopupWithVisitorType (message) {
  const visitorTypeElement = document.getElementById('visitorType');
  const switchToReturningElement = document.getElementById('returningLink');
  visitorTypeElement.textContent = "You are a " + message.data.type;

  if (message.data.type === "New visitor") {
    switchToReturningElement.classList.remove('d-none');
  } else {
    switchToReturningElement.classList.add('d-none');
  }  
}

function populateCampaignTable(campaigns) {
  const tableBody = document.getElementById("tableBody");
  tableBody.innerHTML = "";
  
  for (let i = 0; i < campaigns.length; i++) {
    const campaign = campaigns[i];
    const row = `<tr>
                    <td>${campaign.id}</td>
                    <td>${campaign.name}</td>
                    <td>${campaign.variant}</td>
                </tr>`;
    tableBody.innerHTML += row;

  }
}

function populateCampaignSelect(campaigns) {
  const valCampaigns = document.getElementById("valCampaigns");
  valCampaigns.innerHTML = "";
  valCampaigns.innerHTML = "<option selected>Select a campaign</option>";

  for (let i = 0; i < campaigns.length; i++) {
    const campaign = campaigns[i];

    if (!campaign.isInline) {
      const option = `<option value="${campaign.id}">${campaign.name} (id: ${campaign.id})</option>`
      valCampaigns.innerHTML += option
    }

  }
}

function populateEventTable(OmEvents) {
  const eventsTableBody = document.getElementById("eventsTableBody");
  eventsTableBody.innerHTML = "";

  for (let i = 0; i < OmEvents.length; i++) {
    const OmEvent = OmEvents[i];
    const dateTime = timestampToDateTime(OmEvent.timestamp);

    const row = `<tr>
                    <td>${OmEvent.event}</td>
                    <td>${OmEvent.type}</td>
                    <td>${dateTime}</td>
                </tr>`;
    eventsTableBody.innerHTML += row;
  }
}

function populateAiVariableTable(aiVariables) {
  const aiTableBody = document.getElementById("aiTableBody");
  aiTableBody.innerHTML = "";

  for (let i = 0; i < aiVariables.length; i++) {
    const aiVariable = aiVariables[i];
    const row = `<tr>
                    <td>${aiVariable.variable}</td>
                    <td>${aiVariable.text}</td>
                </tr>`;
    aiTableBody.innerHTML += row;
  }
}

function populateValidatorsTable(campaignValidators) {
  console.log("validators", campaignValidators);

  const validatorTableBody = document.getElementById("validatorTableBody");
  validatorTableBody.innerHTML = "";

  for (let i = 0; i < campaignValidators.length; i++) {
    const campaignValidator = campaignValidators[i];
    let validatorName = campaignValidator.type;

    switch (validatorName) {
      case "viewedPage":
        validatorName = "Current page / URL";
        break;
      
      case "schedule":
        validatorName = "Campaign scheduling";
        break;

      case "subscribers":
        validatorName = "Subscribers / Non-subscribers";
        break;
      
      case "source":
        validatorName = "Source";
        break;

      case "campaignProgressState":
        validatorName = "Engaged with OptiMonk campaigns";
        break;
      
      case "timeBasedActualPage":
        validatorName = "Spent on pages";
        break;

      case "timeBasedSession":
        validatorName = "Spent on site";
        break;
      
      case "previouslyViewedPage":
        validatorName = "Visited URL";
        break;

      case "cookie":
        validatorName = "Cookie segmentation";
        break;
      
      case "visitorAttribute":
        validatorName = "Other visitor attributes";
        break;

      case "aBlock":
        validatorName = "AdBlock detection";
        break;
      
      case "maximumPopupDisplay":
        validatorName = "Frequency #1 number of display";
        break;

      case "loggedIn":
        validatorName = "Shopify logged in user";
        break;
      
      case "visitorInKlaviyoSegmentsOrLists":
        validatorName = "Klaviyo segment";
        break;

      case "assetsLoaded":
        validatorName = "Assets loaded (fonts / images)";
        break;

      case "product":
        validatorName = "Have enough products to recommend?";
        break;

      case "smartProductTagValidator":
        validatorName = "Have data for smart tag?";
        break;

      case "currentlyActive":
        validatorName = "Validate if something is already displayed (including teaser)";
        break;

      case "showable":
        validatorName = "Messenger code installed (if used in campaign)";
        break;
      
      case "timeUntilAvailable":
        validatorName = "Exclude page visitors";
        break;

      case "timeUntilNextAppearance":
        validatorName = "Frequency #2 time between two appearances";
        break;

      case "couponExpiryValidator":
        validatorName = "Is shopify coupon still valid?";
        break;

      case "followupCoupon":
        validatorName = "There are any coupon to follow up? (if needed)";
        break;

      case "coupon":
        validatorName = "Are there any available coupons?";
        break;

      case "fill":
        validatorName = "The campaign has not been filled yet";
        break;

      case "countdown":
        validatorName = "Is the countdown date valid?";
        break;

      case "experiment":
        validatorName = "Experiment";
        break;

      case "globalFrequencyCap":
        validatorName = "User Experience Protector";
        break;

      case "minimumPageVisit":
        validatorName = "Number of visited pages";
        break;

      case "notViewedPageRecent":
        validatorName = "Exclude page visitors";
        break;

      case "visitorCartV3":
        validatorName = "Cart rule";
        break;

      case "country":
        validatorName = "Country";
        break;
      
      case "pageViewerType":
        validatorName = "Returning / New";
        break;

      case "queuedCampaignType":
        continue;

      case "activated":
        continue;

      case "enhancedPageViews":
        continue;
      
      case "visitorCart":
        continue;
      
      case "visitorCartRevamp":
        continue;
    }

    let falseChecker = "";

    if (campaignValidator.result === undefined) {
      campaignValidator.result = campaignValidator.valid;
    }

    if (campaignValidator.result === false) {
      falseChecker = "validator-failed";
    }
    const row = `<tr>
                    <td>${validatorName}</td>
                    <td class="${falseChecker}">${campaignValidator.result}</td>
                </tr>`;
    validatorTableBody.innerHTML += row;
  }

}

function timestampToDateTime(timestamp) {

  // Create a new Date object using the timestamp
  const date = new Date(timestamp);

  // Extract the various components of the date
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // Months are zero-indexed, so add 1
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();
  const milliseconds = date.getMilliseconds();

  // Format the date and time as a string
  const formattedDateTime = `${year}-${pad(month)}-${pad(day)} ${pad(hours)}:${pad(minutes)}:${pad(seconds)}.${pad3(milliseconds)}`;

  return formattedDateTime;

  // Helper function to pad single digits with leading zeros
  function pad(number) {
    return number < 10 ? `0${number}` : number;
  }

  // Helper function to pad milliseconds to three digits
  function pad3(number) {
    if (number < 10) {
      return `00${number}`;
    } else if (number < 100) {
      return `0${number}`;
    } else {
      return number;
    }
  }
}


function getFreshData() {
  chrome.tabs.query({currentWindow: true, active: true}, function (tabs){
      var activeTab = tabs[0];
      chrome.tabs.sendMessage(activeTab.id, {"action": "refresh"});
  });
}

function sendClearCookiesAction() {
  chrome.tabs.query({currentWindow: true, active: true}, function (tabs){
    var activeTab = tabs[0];
    chrome.tabs.sendMessage(activeTab.id, {"action": "clear-all-om-cookies"});
  });
}

function sendSwitchToReturningAction () {
  chrome.tabs.query({currentWindow: true, active: true}, function (tabs){
    var activeTab = tabs[0];
    chrome.tabs.sendMessage(activeTab.id, {"action": "switch-to-returning-visitor"});
  });
}

function getCampaignValidators(campaignId) {
  chrome.tabs.query({currentWindow: true, active: true}, function (tabs){
    var activeTab = tabs[0];
    chrome.tabs.sendMessage(activeTab.id, {"action": "get-campaign-validators", "campaignId": campaignId});
  });
}

// Listen for messages from the inject script
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {

  if (message.type === 'detected-platform') {
    updatePopupWithPlatform(message);
  }

  if (message.type === 'optimonk_account') {
    updatePopupWithAccountValue(message);
  }

  if (message.type === 'optimonk_visitor_type') {
    updatePopupWithVisitorType(message);
  }

  if (message.type === 'optimonk_campaigns') {
    populateCampaignTable(message.data);
    populateCampaignSelect(message.data);
  }

  if (message.type === 'optimonk_events') {
    populateEventTable(message.data);
  }

  if (message.type === 'ai_variables') {
    populateAiVariableTable(message.data);
  }

  if (message.type === 'asked_campaign_validators') {
    populateValidatorsTable(message.data);
  }
});

//If extension popup opens, send refresh action to get fresh data from the site
document.addEventListener("DOMContentLoaded", function() {
  getFreshData();

  document.getElementById('clearOmCookies').addEventListener('click', function() {
    sendClearCookiesAction();
  });

  document.getElementById('returningLink').addEventListener('click', function() {
    sendSwitchToReturningAction();
  });

});
