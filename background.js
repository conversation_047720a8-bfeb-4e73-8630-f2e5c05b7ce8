chrome.webRequest.onBeforeRequest.addListener(
  function(details) {
    const rawEvent = details.requestBody?.formData?.events?.[0];
    try {
      const objEvent = JSON.parse(rawEvent);
      
      if (objEvent.length > 0) {
        chrome.tabs.query({ currentWindow: true, active: true }, function(tabs) {
          var activeTab = tabs[0];
            chrome.tabs.sendMessage(
              activeTab.id,
              { action: "saveRequestData", data: objEvent, timestamp: details.timeStamp, type: "jetfabric" },
              function(response) {
                if (chrome.runtime.lastError) {
                  console.error('Error sending message:', chrome.runtime.lastError.message);
                } else {
                  console.log('Message sent to content script');
                }
              }
            );
        });
      }
    } catch (error) {
      console.error('Error parsing JSON from v2 request:', error);
      console.log('Raw event data:', rawEvent);
      return; // Exit early if parsing fails
    }
  },
  { urls: ["https://jfapiprod.optimonk.com/v2/"] },
  ["requestBody"]
);

chrome.webRequest.onCompleted.addListener(
  function(details) {

    fetch(details.url + '?debugextension=1').then(r => r.text()).then(result => {
    // Result now contains the response text, do what you want...
      //console.log(JSON.parse(result));
    })

    // TODO use accountinfo if needed 
  },
  { urls: ["https://cdn-account.optimonk.com/*/accountInfo.json"] },
  ["responseHeaders"]
);

chrome.webRequest.onBeforeRequest.addListener(
  function(details) {
    var trackedParams = new URLSearchParams(details.url);

    //console.log(details);

/*

    if (typeof details.requestBody.raw !== 'undefined') {
      let arrayBuffer = details.requestBody.raw[0].bytes
      let decoder = new TextDecoder('utf-8');
      let decodedString = decoder.decode(arrayBuffer);

      function parseEvents(inputString) {
        // Split the string into individual events
        const events = inputString.split('en=optimonk_campaign_event&_ee=1&');
    
        // Initialize array to store parsed events
        const parsedEvents = [];
    
        // Iterate over each event and extract property values
        events.forEach(event => {
            const campaignNameMatch = event.match(/ep\.om_campaign_name=(.*?)(&|$)/);
            const campaignActionMatch = event.match(/ep\.om_campaign_action=(.*?)(&|$)/);
            const campaignIdMatch = event.match(/epn\.om_campaign_id=(.*?)(&|$)/);
            const campaignVariantMatch = event.match(/ep\.om_campaign_variant_name=(.*?)(&|$)/);
    
            if (campaignNameMatch && campaignActionMatch && campaignIdMatch && campaignVariantMatch) {
                const decodedCampaignName = decodeURIComponent(campaignNameMatch[1]);
                const campaignAction = campaignActionMatch[1];
                const campaignId = campaignIdMatch[1];
                const decodedCampaignVariant = decodeURIComponent(campaignVariantMatch[1]);
    
                parsedEvents.push({
                    campaignName: decodedCampaignName,
                    campaignAction: campaignAction,
                    campaignId: campaignId,
                    campaignVariant: decodedCampaignVariant
                });
            }
        });
    
        return parsedEvents;
    } */

    if (trackedParams.get('en') === "optimonk_campaign_event") {

      const objEvent = [{
        campaignName: trackedParams.get("ep.om_campaign_name"),
        variantName: trackedParams.get("ep.om_campaign_variant_name"),
        campaignId: trackedParams.get("epn.om_campaign_id"),
        event: trackedParams.get("ep.om_campaign_action") 
      }];

      //console.log(objEvent);
      
      /*chrome.tabs.query({currentWindow: true, active: true}, function (tabs){
        var activeTab = tabs[0];
        chrome.tabs.sendMessage(activeTab.id, {action: "saveRequestData", data: objEvent, timestamp: details.timeStamp, type: "google"});
      });*/
    }
  },
  { urls: ["https://*/*/collect?*"] },
  ["requestBody"]
);
