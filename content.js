var OptiMonkOnReady = function () {
  OptiMonkRegistry.isDebug = true
};

if (typeof window.OptiMonkRegistry !== 'undefined') {
  OptiMonkRegistry.isDebug = true
}

function sendPlatformData () {
  let detectedPlatform = "Custom"

  if (window.Shopify) {
    detectedPlatform = "Shopify"
  }

  if (window.ShopRenter) {
    detectedPlatform = "Shoprenter"
  }
  
  if (document.querySelectorAll('[href*="wp-content"]').length > 0 || document.querySelectorAll('[href*="wp-includes"]').length > 0) {
    detectedPlatform = "Wordpress"
  }

  if (window.UNAS) {
    detectedPlatform = "Unas"
  }

  if (window.BCData) {
    detectedPlatform = "Bigcommerce"
  }

  if (window.prestashop) {
    detectedPlatform = "Prestashop"
  }

  window.postMessage({
    source : 'om-diagnostic',
    type : 'detected-platform', 
    platform: detectedPlatform
  });

}

// Function to check if OptiMonkRegistry.account exists and send its value to the popup
function sendAccountData() {
  let accountValue = null;
  let isEmbedExists = false;
  let environment = null;

  if (typeof window.OptiMonkRegistry !== 'undefined' && window.OptiMonkRegistry.account) {
    accountValue = window.OptiMonkRegistry.account;

    // Extract environment information from beUrl
    if (window.OptiMonkRegistry.beUrl) {
      const beUrl = window.OptiMonkRegistry.beUrl;

      if (beUrl === 'https://backend.optimonk.com') {
        environment = 'live';
      } else {
        // Extract environment prefix from URLs like staging4-backend.optimonk.com or qa1-backend-optimonk.com
        const match = beUrl.match(/([a-zA-Z0-9]+)-backend/);
        if (match) {
          environment = match[1];
        }
      }
    }
  }

  if (typeof window.OptiMonkEmbedded !== 'undefined') {
    isEmbedExists = true;
  }

  // Send message containing OptiMonk account value to the extension popup
  window.postMessage({
    source : 'om-diagnostic',
    type : 'optimonk_account',
    data: {
      accountId : accountValue,
      isEmbedExists : isEmbedExists,
      environment : environment
    }
  });
}

function sendVisitorData() {
  let accountValue = null;
  let visitorType = ''

  if (typeof window.OptiMonkRegistry !== 'undefined' && window.OptiMonkRegistry.account) {
    accountValue = window.OptiMonkRegistry.account;
  }

  if (accountValue !== null) {
    let differenceInHour = getVisitorTypeFromCookie(accountValue);
    visitorType = differenceInHour > 24 ? 'Returning visitor' : 'New visitor';
  }

  window.postMessage({
    source : 'om-diagnostic',
    type : 'optimonk_visitor_type', 
    data: {
      type : visitorType,
    }
  });

}

function sendCampaigns() {
  let campaings = null;
  let embedCampaigns = null;
  let allCampaigns = [];

  if (typeof window.OptiMonk?.campaigns !== 'undefined') {
    campaigns = window.OptiMonk.campaigns
    let keys = Object.keys(campaigns);

    for (let i = 0; i < keys.length; i++) {
      allCampaigns.push({
      "id": campaigns[keys[i]].campaignId, 
      "name": campaigns[keys[i]].campaignName,
      "variant": campaigns[keys[i]].creativeName,
      "isInline": false
      })
    }
  
  }

  if (typeof window.OptiMonkEmbedded?.campaigns !== 'undefined') {
    embedCampaigns = window.OptiMonkEmbedded.campaigns
    let embedKeys = Object.keys(embedCampaigns);
    let actualDomain = window.location.hostname;

    if (actualDomain.substring(0,4) === 'www.') {
      actualDomain = actualDomain.substring(4);
    }

    for (let i = 0; i < embedKeys.length; i++) {
      if (embedCampaigns[embedKeys[i]].domain === actualDomain) {
        allCampaigns.push({
          "id": embedCampaigns[embedKeys[i]].id, 
          "name": embedCampaigns[embedKeys[i]].name,
          "variant": embedCampaigns[embedKeys[i]].variantName,
          "isInline": true
        })
      }
    }
  }

  window.postMessage({
    source : 'om-diagnostic',
    type : 'optimonk_campaigns', 
    data: allCampaigns
  });

}

function sendOmEvents() {
  window.postMessage({
    source : 'om-diagnostic',
    type : 'optimonk_events', 
    data: OmEvents
  });
}

function sendAiVariables() {
  if(window.OptiMonk?.Visitor?.Attributes) {
    const allVariable = window.OptiMonk.Visitor.Attributes.all()
    const aiVariables = [];
    let keys = Object.keys(allVariable);

    for (let i = 0; i < keys.length; i++) {
      if (keys[i].startsWith("sppo")) {
        aiVariables.push({
          "variable": keys[i], 
          "text": allVariable[keys[i]]
        })
      } 
    }
    
    window.postMessage({
      source : 'om-diagnostic',
      type : 'ai_variables', 
      data: aiVariables
    });
  }
}

function getOptiMonkCookies() {
  var allCookies = document.cookie;
  var cookiesArray = allCookies.split(';');
  var optiMonkCookies = [];

  cookiesArray.forEach(function(cookie) {
      cookie = cookie.trim();

      if (cookie.startsWith('optiMonk')|| cookie.startsWith('omAbTest')) {
          optiMonkCookies.push(cookie.split('=')[0]);
      }
  });

  return optiMonkCookies;
}

function getVisitorTypeFromCookie(accountId) {

  var omCookie = OptiMonkEmbedded.CookieManager.getCookies()

  const firstVisit = omCookie[accountId].fv;
  const lastVisit = omCookie[accountId].lv;

  const firstVisitInMillisec = new Date(firstVisit * 1000);
  const lastVisitInMillisec = new Date(lastVisit * 1000);

  const diffInMilliseconds = lastVisitInMillisec - firstVisitInMillisec;
  const diffInHours = diffInMilliseconds / (1000 * 60 * 60);

  return Math.trunc(diffInHours);
}

function clearOptiMonkCookies() {
  var optimonkCookies = getOptiMonkCookies();

  function removeCookie(cookieName) {
    window.cookieStore.delete(cookieName);
  }

  for (var i = 0; i < optimonkCookies.length; i++) {
    removeCookie(optimonkCookies[i]);
  }

  window.location.reload();
  sendVisitorData();
}

function switchToReturning() {
  let accountId = window.OptiMonkRegistry.account;
  let OptiMonkCookie = OptiMonkEmbedded.CookieManager.getCookies()[accountId];

  function calculateTwoDaysAgoTimestamp() {
   
    const currentTimestamp = Date.now();
    const twoDaysInMillis = *********;
    const twoDaysAgoTimestamp = currentTimestamp - twoDaysInMillis;

    return Math.floor(twoDaysAgoTimestamp / 1000); //convert to seconds
  }

  OptiMonkCookie.fv = calculateTwoDaysAgoTimestamp();
  OptiMonkRegistry.Cookie = OptiMonkCookie;


  OptiMonkEmbedded.CookieManager.saveCookie({
    [accountId]: {
      ca: OptiMonkCookie.ca,
      fv: OptiMonkCookie.fv,
      lv: OptiMonkCookie.lv,
      nopv: OptiMonkCookie.nopv,
    },
  });

  window.location.reload();
  sendVisitorData();
}


function sendSelectedCampaignValidators(campaignId) {
  const validators = window.omdebug[campaignId];

  window.postMessage({
    source : 'om-diagnostic',
    type : 'asked_campaign_validators', 
    data: validators
  });
}

var OmEvents = []
function saveCapturedRequestData(requestData) {

  if (requestData.type === 'jetfabric') {
    for (let i = 0; i < requestData.data.length; i++) {
      OmEvents.push({
        "event": requestData.data[i].type, 
        "timestamp": requestData.timestamp,
        "type": requestData.type
      })
    }
  } 

  if (requestData.type === 'google') {
    for (let i = 0; i < requestData.data.length; i++) {
      OmEvents.push({
        "event": requestData.data[i].campaignName + " " + requestData.data[i].event, 
        "timestamp": requestData.timestamp,
        "type": requestData.type
      })
    }
  }

}

//Handle messages from the extension
window.addEventListener("message", (event) => {

  if (event.data.action === "saveRequestData") {
    saveCapturedRequestData(event.data);
    sendOmEvents();
  }

  if (event.data.action === "get-campaign-validators") {
    sendSelectedCampaignValidators(event.data.campaignId);
  }

  if (event.data.action === "clear-all-om-cookies") {
    clearOptiMonkCookies();
  }

  if (event.data.action === "switch-to-returning-visitor") {
    switchToReturning();
  }

  if (event.data.action === "refresh") {
    sendAccountData();
    sendCampaigns();
    sendOmEvents();
    sendAiVariables();
    sendPlatformData();
    sendVisitorData();
  }

}, false);
