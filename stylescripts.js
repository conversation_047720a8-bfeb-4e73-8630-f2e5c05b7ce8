document.addEventListener('DOMContentLoaded', function() {
    // Get tab links and tab content elements
    const tabLinks = document.querySelectorAll('.nav-link');
    const tabContents = document.querySelectorAll('.tab-pane');

    // Loop through each tab link to add click event listener
    tabLinks.forEach(function(tabLink) {
        tabLink.addEventListener('click', function(event) {
            event.preventDefault(); // Prevent default anchor behavior

            // Remove 'active' class from all tab links
            tabLinks.forEach(function(link) {
                link.classList.remove('active');
            });

            // Add 'active' class to the clicked tab link
            this.classList.add('active');

            // Hide all tab content panes
            tabContents.forEach(function(tabContent) {
                tabContent.classList.remove('show', 'active');
            });

            // Show the corresponding tab content based on the clicked tab
            const tabTargetId = this.getAttribute('href').substring(1); // Get target tab ID without '#'
            const targetTabContent = document.getElementById(tabTargetId);

            // Add 'show' and 'active' class to the target tab content
            targetTabContent.classList.add('show', 'active');
        });
    });

    var selectElement = document.getElementById('valCampaigns');
  
    // Add a change event listener to the select element
    selectElement.addEventListener('change', function(event) {
    // Get the selected option value
    var selectedOption = event.target.value;
    
    getCampaignValidators(event.target.value);

    // Example: Display the selected option value
    console.log('Selected option:', selectedOption);

  });

});