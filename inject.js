function injectScript(file_path, tag) {
    var node = document.getElementsByTagName(tag)[0];
    var script = document.createElement('script');
    script.setAttribute('type', 'text/javascript');
    script.setAttribute('src', file_path);
    node.appendChild(script);
}

injectScript(chrome.runtime.getURL('content.js'), 'body');

//Pass data forward to popup js
window.addEventListener("message", (event) => {
  if (event.data.source === 'om-diagnostic') {
    chrome.runtime.sendMessage(event.data);
  }
}, false);

//Pass data forward to content js
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
  window.postMessage(message);
  sendResponse({
    response: "Message received"
  });
});